"ui";

importClass(java.io.File);

// 全局变量
var selectedFile = null; // 添加全局变量定义
var mainActivity = null; // 保存主界面引用
var running = false; // 脚本运行状态
var currentProgress = 0; // 当前进度
var hasBeenPaused = false; // 是否暂停过

// 清理缓存和临时文件
function cleanupFiles() {
  // 清理旧的记录文本文件，防止其过大
  var jl_path = "/sdcard/Pictures/记录文本.txt";
  if (files.exists(jl_path)) {
    // 直接删除记录文件，不需要检查大小
    files.remove(jl_path);
  }

  // 清理Auto.js的日志文件
  var logDir = "/sdcard/Android/data/org.autojs.autojs/files/logs/";
  if (files.exists(logDir)) {
    var logFiles = files.listDir(logDir);
    var deletedCount = 0;
    for (var i = 0; i < logFiles.length; i++) {
      if (logFiles[i].endsWith(".log")) {
        files.remove(logDir + logFiles[i]);
        deletedCount++;
      }
    }
    if (deletedCount > 0) {
      console.info("已清理" + deletedCount + "个日志文件");
    }
  }

  // 清理控制台缓存
  console.clear();
}

// 不在脚本启动时清理文件，而是在选择文件后启动时清理

// 初始化界面元素
ui.layout(
  <vertical bg="#ffffff" padding="16" marginTop="5">
    <text text="自动化工具启动界面" textSize="24sp" textColor="#000000" gravity="center" typeface="serif"/>
    <button id="selectFileBtn" text="请选择单号文件" marginTop="16" style="Widget.AppCompat.Button.Colored" textSize="18sp" typeface="serif"/>
    <list id="fileList" h="320" marginTop="8">
      <horizontal>
        <text id="fileName" text="{{fileName}}" textColor="#000000" textSize="18sp" padding="8" typeface="serif"/>
      </horizontal>
    </list>
    <text id="selectedFileText"
      text="已选择单号文件："
      textColor="#ffffff"
      bg="#4CAF50"
      padding="8"
      marginTop="12"
      textSize="18sp"
      typeface="serif"
      lines="2"
      ellipsize="end"
      w="*"
      h="auto"
      gravity="left"/>
    <horizontal marginTop="24" gravity="center">
      <button id="btnJibao" text="韵达集包" bg="#FFEB3B" textColor="#000000" w="0" layout_weight="1" marginRight="6" textSize="18sp" typeface="serif"/>
      <button id="btnDaopai" text="韵达到派" bg="#FFEB3B" textColor="#000000" w="0" layout_weight="1" marginRight="6" textSize="18sp" typeface="serif"/>
      <button id="btnXiangzhen" text="乡镇优化" bg="#FFEB3B" textColor="#000000" w="0" layout_weight="1" textSize="18sp" typeface="serif"/>
    </horizontal>
  </vertical>
);

// 加载 txt 文件列表
function loadTxtFiles() {
  let dir = new File("/sdcard/Pictures");
  if (!dir.exists() || !dir.isDirectory()) {
    toast("目标文件夹不存在！");
    return [];
  }

  let files = dir.listFiles();
  let txtFiles = [];

  if (files) {
    for (let f of files) {
      if (f.isFile() && f.getName().endsWith(".txt")) {
        txtFiles.push({
          fileName: f.getName(),
          fullPath: f.getAbsolutePath()
        });
      }
    }
  }
  return txtFiles;
}

// 文件选择逻辑
ui.selectFileBtn.on("click", () => {
  let fileList = loadTxtFiles();
  if (fileList.length === 0) {
    toast("未找到 txt 文件");
  } else {
    ui.fileList.setDataSource(fileList);
  }
});

ui.fileList.on("item_click", (item) => {
  selectedFile = item.fullPath;
  // 始终保持两行显示
  ui.selectedFileText.setText("已选择单号文件：\n" + item.fileName);
  toast("已选择: " + item.fileName);
});

// 韵达集包按钮
ui.btnJibao.on("click", () => {
  if (!selectedFile) return toast("请先选择单号文件！");

  // 清理旧文件
  cleanupFiles();

  // 直接删除记录文件，确保每次选择新文件后都从头开始
  var jl_path = "/sdcard/Pictures/记录文本.txt";
  if (files.exists(jl_path)) {
    files.remove(jl_path);
  }

  toast("启动韵达集包");

  // 保存主界面引用
  mainActivity = activity;

  // 先创建悬浮窗，再隐藏主界面
  setTimeout(function() {
    launchFloatingWindow(selectedFile);
    // 使用setVisibility而非ui.finish()来隐藏主界面
    ui.setContentView(ui.inflate(<frame/>));
  }, 500);
});

// 占位逻辑
ui.btnDaopai.on("click", () => {
  if (!selectedFile) return toast("请先选择单号文件！");
  toast("执行：韵达到派\n文件：" + selectedFile);

  // 保存主界面引用
  mainActivity = activity;

  // 使用相同的方式隐藏主界面
  setTimeout(function() {
    ui.setContentView(ui.inflate(<frame/>));
  }, 500);
});

ui.btnXiangzhen.on("click", () => {
  if (!selectedFile) return toast("请先选择单号文件！");
  toast("执行：乡镇优化\n文件：" + selectedFile);

  // 保存主界面引用
  mainActivity = activity;

  // 使用相同的方式隐藏主界面
  setTimeout(function() {
    ui.setContentView(ui.inflate(<frame/>));
  }, 500);
});

// ============ 创建悬浮窗逻辑 ============

function launchFloatingWindow(filePath) {
  var window = floaty.window(
    <horizontal>
      <button id="start" text="启动" w="80" h="50" />
      <button id="pause" text="暂停" w="80" h="50" />
      <button id="closeBtn" text="关闭" w="80" h="50" />
    </horizontal>
  );

  window.setPosition(10, 10);

  // 保持 UI 活动
  setInterval(() => {}, 1000);

  // 状态变量
  running = false; // 改为全局变量
  var scriptThread = null;

  // 重置当前进度
  currentProgress = 0;
  hasBeenPaused = false;

  window.start.click(() => {
    if (!running) {
      // 如果之前暂停过，显示选择弹窗
      if (hasBeenPaused) {
        // 使用悬浮窗而非对话框来避免 BadTokenException
        let startOptionsWindow = floaty.window(
          <vertical bg="#00000000" padding="0" w="100">
            <button id="fromStartBtn" text="从头开始" bg="#2196F3" textColor="#FFFFFF" w="*" h="40" marginBottom="4" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
            <button id="continueBtn" text="断点继续" bg="#4CAF50" textColor="#FFFFFF" w="*" h="40" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
          </vertical>
        );

        // 使用与关闭按钮相同的位置
        startOptionsWindow.setPosition(17, 105);

        // 从头开始按钮
        startOptionsWindow.fromStartBtn.click(() => {
          startOptionsWindow.close();
          running = true;
          toast("从头开始运行脚本");

          // 重置进度
          currentProgress = 0;

          scriptThread = threads.start(function () {
            console.show();
            console.setPosition(5, 825);

            // 设置控制台标题为文件名(不含.txt)
            var fileName = filePath.split("/").pop().replace(".txt", "");
            console.setTitle(fileName);

            runJibaoScript(filePath, 0); // 从头开始
          });
        });

        // 断点继续按钮
        startOptionsWindow.continueBtn.click(() => {
          startOptionsWindow.close();
          running = true;
          toast("断点继续运行脚本");

          // 显示断点位置信息
          console.log("断点继续，从第 " + (currentProgress + 1) + " 条开始");

          scriptThread = threads.start(function () {
            console.show();
            console.setPosition(5, 825);

            // 设置控制台标题为文件名(不含.txt)
            var fileName = filePath.split("/").pop().replace(".txt", "");
            console.setTitle(fileName);

            runJibaoScript(filePath, currentProgress); // 从断点继续
          });
        });
      } else {
        // 首次启动，直接运行
        running = true;
        toast("脚本已启动");

        scriptThread = threads.start(function () {
          console.show();
          console.setPosition(5, 825);

          // 设置控制台标题为文件名(不含.txt)
          var fileName = filePath.split("/").pop().replace(".txt", "");
          console.setTitle(fileName);

          runJibaoScript(filePath, 0);
        });
      }
    }
  });

  window.pause.click(() => {
    if (running) {
      running = false;
      hasBeenPaused = true; // 标记已经暂停过
      toast("脚本已暂停");

      // 强制中断脚本线程
      if (scriptThread) {
        scriptThread.interrupt();
        // 添加额外的中断机制
        threads.shutDownAll();
      }
    }
  });

  window.closeBtn.click(() => {
    // 使用悬浮窗而非对话框来避免 BadTokenException
    let optionsWindow = floaty.window(
      <vertical bg="#00000000" padding="0" w="100">
        <button id="returnBtn" text="返回选择" bg="#2196F3" textColor="#FFFFFF" w="*" h="40" marginBottom="4" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
        <button id="exitBtn" text="退出脚本" bg="#F44336" textColor="#FFFFFF" w="*" h="40" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
      </vertical>
    );

    // 将悬浮窗定位在屏幕中心
    // 直接使用固定值作为X和Y坐标，调整以适应新的宽度
    optionsWindow.setPosition(17,105);

    // 返回主界面按钮
    optionsWindow.returnBtn.click(() => {
      // 关闭所有悬浮窗
      optionsWindow.close();
      window.close();

      // 关闭控制台窗口
      console.hide();

      // 重新启动主界面
      try {
        // 使用延时确保悬浮窗已关闭
        setTimeout(function() {
          engines.execScriptFile(engines.myEngine().getSource());
          // 等待新脚本启动后再停止当前脚本
          setTimeout(function() {
            engines.myEngine().forceStop();
          }, 500);
        }, 100);
      } catch (e) {
        toast("返回主界面失败: " + e);
      }
    });

    // 退出脚本按钮
    optionsWindow.exitBtn.click(() => {
      toast("脚本已退出");
      optionsWindow.close();
      window.close();

      // 关闭控制台窗口
      console.hide();

      // 关闭所有线程并退出
      threads.shutDownAll();
      exit();
    });
  });
}

// ============ 主脚本逻辑 ============

function runJibaoScript(filePath, startIndex) {
  var jl_path = "/sdcard/Pictures/记录文本.txt";
  var jl_hs = 0;

  // 创建记录文件
  if (files.create(jl_path)) {
    sleep(150);
  } else {
    try {
      var arr_jl = open(jl_path).readlines();
      jl_hs = arr_jl.length === 0 ? 0 : parseInt(arr_jl[arr_jl.length - 1]) + 1;
    } catch (e) {
      // 如果读取文件出错，重新创建文件
      files.remove(jl_path);
      files.create(jl_path);
      jl_hs = 0;
    }
  }

  // 如果提供了起始索引，则使用它，否则使用记录文件中的进度
  var startPos = (startIndex !== undefined && startIndex > 0) ? startIndex : jl_hs;

  var arr_a = open(filePath).readlines();
  console.log("当前单号文本数量: " + arr_a.length);
  console.log("从第 " + (startPos + 1) + " 条开始处理");

  for (var i = startPos; i < arr_a.length; i++) {
    // 检查是否已暂停
    if (!running) {
      console.log("脚本已暂停，当前进度: " + (i + 1) + "/" + arr_a.length);
      console.log("断点位置已保存，可以继续从第 " + (i + 1) + " 条继续");
      break;
    }

    // 更新全局进度变量，以便暂停后可以继续
    currentProgress = i;

    var user = id("took_shipment_collection_bill_number").findOne();
    console.warn("读取单号: " + arr_a[i]);
    console.warn("当前进度: " + (i + 1) + "/" + arr_a.length);

    user.setText(arr_a[i]);
    sleep(5);
    id("took_shipment_collection_add").findOne().click();
    sleep(5);

    var cx = text("确定").findOne(135);
    if (cx !== null) {
      cx.click();
    }

    // 每处理多条记录才写入一次文件，减少频繁IO
    if (i % 1000 === 0 || i === arr_a.length - 1) {
      files.append(jl_path, i + "\n");
    }

    // 添加小延时，使暂停检测更可靠
    sleep(5);
  }

  console.log("结束");
  // 重置标记，下次启动时不再显示选择弹窗
  hasBeenPaused = false;
}

